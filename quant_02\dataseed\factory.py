"""
数据源工厂模块

负责数据源的注册、创建和管理。
"""

from typing import Dict, Type, Any, Optional
import importlib

from .base import DataSource
from .mock import MockDataSource
from ..core.exceptions import DataSourceException


class DataSourceFactory:
    """数据源工厂
    
    负责数据源的注册、创建和管理。
    """
    
    def __init__(self):
        self._sources: Dict[str, Type[DataSource]] = {}
        
        # 注册内置数据源
        self._register_builtin_sources()
        
        # 初始化日志
        self._init_logger()
        self.logger.info("数据源工厂初始化完成")
    
    def _init_logger(self):
        """初始化日志"""
        from ..utils.logger import get_logger
        self.logger = get_logger(__name__)
    
    def _register_builtin_sources(self):
        """注册内置数据源"""
        # 注册Mock数据源
        self.register("mock", MockDataSource)
        
        # 尝试注册AkShare数据源
        try:
            from .akshare import AkShareDataSource
            self.register("akshare", AkShareDataSource)
        except ImportError:
            self.logger.warning("AkShare数据源不可用，请安装akshare: pip install akshare")
        
        # 尝试注册数据库数据源
        try:
            from .database import DatabaseDataSource
            self.register("database", DatabaseDataSource)
        except ImportError:
            self.logger.warning("数据库数据源不可用，请安装相关依赖")
        
        self.logger.info("内置数据源注册完成")
    
    def register(self, name: str, source_class: Type[DataSource]):
        """注册数据源
        
        Args:
            name: 数据源名称
            source_class: 数据源类
        """
        if not issubclass(source_class, DataSource):
            raise DataSourceException(f"数据源类必须继承自DataSource: {source_class}")
        
        self._sources[name] = source_class
        self.logger.info(f"数据源已注册: {name}")
    
    def unregister(self, name: str):
        """注销数据源
        
        Args:
            name: 数据源名称
        """
        if name in self._sources:
            del self._sources[name]
            self.logger.info(f"数据源已注销: {name}")
        else:
            self.logger.warning(f"数据源不存在: {name}")
    
    def create(self, name: str, **kwargs) -> DataSource:
        """创建数据源实例
        
        Args:
            name: 数据源名称
            **kwargs: 数据源参数
            
        Returns:
            数据源实例
        """
        if name not in self._sources:
            raise DataSourceException(f"未注册的数据源: {name}")
        
        source_class = self._sources[name]
        
        try:
            # 创建实例
            instance = source_class(name=name, **kwargs)
            self.logger.info(f"数据源实例已创建: {name}")
            return instance
            
        except Exception as e:
            self.logger.error(f"数据源创建失败: {name}, 错误: {e}")
            raise DataSourceException(f"数据源创建失败: {e}")
    
    def list_available(self) -> Dict[str, str]:
        """列出可用的数据源
        
        Returns:
            数据源名称到类名的映射
        """
        return {name: cls.__name__ for name, cls in self._sources.items()}
    
    def is_available(self, name: str) -> bool:
        """检查数据源是否可用
        
        Args:
            name: 数据源名称
            
        Returns:
            是否可用
        """
        return name in self._sources
    
    def get_source_info(self, name: str) -> Dict[str, Any]:
        """获取数据源信息
        
        Args:
            name: 数据源名称
            
        Returns:
            数据源信息
        """
        if name not in self._sources:
            return {'error': f'数据源不存在: {name}'}
        
        source_class = self._sources[name]
        
        return {
            'name': name,
            'class': source_class.__name__,
            'module': source_class.__module__,
            'doc': source_class.__doc__ or '无描述',
        }
    
    def create_from_config(self, config: Dict[str, Any]) -> DataSource:
        """从配置创建数据源
        
        Args:
            config: 配置字典，必须包含'type'字段
            
        Returns:
            数据源实例
        """
        if 'type' not in config:
            raise DataSourceException("配置中缺少'type'字段")
        
        source_type = config['type']
        source_config = config.copy()
        del source_config['type']
        
        return self.create(source_type, **source_config)
    
    def register_from_module(self, module_name: str, class_name: str, source_name: Optional[str] = None):
        """从模块注册数据源
        
        Args:
            module_name: 模块名
            class_name: 类名
            source_name: 数据源名称，默认使用类名的小写
        """
        try:
            module = importlib.import_module(module_name)
            source_class = getattr(module, class_name)
            
            if source_name is None:
                source_name = class_name.lower()
            
            self.register(source_name, source_class)
            
        except ImportError as e:
            self.logger.error(f"模块导入失败: {module_name}, 错误: {e}")
            raise DataSourceException(f"模块导入失败: {e}")
        except AttributeError as e:
            self.logger.error(f"类不存在: {class_name}, 错误: {e}")
            raise DataSourceException(f"类不存在: {e}")
    
    def test_source(self, name: str, **kwargs) -> bool:
        """测试数据源
        
        Args:
            name: 数据源名称
            **kwargs: 数据源参数
            
        Returns:
            测试是否成功
        """
        try:
            source = self.create(name, **kwargs)
            result = source.test_connection()
            self.logger.info(f"数据源测试: {name}, 结果: {'成功' if result else '失败'}")
            return result
            
        except Exception as e:
            self.logger.error(f"数据源测试失败: {name}, 错误: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取工厂统计信息
        
        Returns:
            统计信息
        """
        return {
            'registered_sources': len(self._sources),
            'available_sources': list(self._sources.keys()),
        }


# 全局数据源工厂实例
data_source_factory = DataSourceFactory()


# 便捷函数
def create_data_source(name: str, **kwargs) -> DataSource:
    """创建数据源实例的便捷函数
    
    Args:
        name: 数据源名称
        **kwargs: 数据源参数
        
    Returns:
        数据源实例
    """
    return data_source_factory.create(name, **kwargs)


def list_data_sources() -> Dict[str, str]:
    """列出可用数据源的便捷函数
    
    Returns:
        数据源名称到类名的映射
    """
    return data_source_factory.list_available()


def register_data_source(name: str, source_class: Type[DataSource]):
    """注册数据源的便捷函数
    
    Args:
        name: 数据源名称
        source_class: 数据源类
    """
    data_source_factory.register(name, source_class)
