"""
Quant_02 主入口文件

提供命令行接口和快速开始功能。
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.config.global_config import GlobalConfig
from dataseed.factory import create_data_source
from utils.logger import get_logger, init_logger


def setup_logging(debug: bool = False):
    """设置日志"""
    log_config = {
        'level': 'DEBUG' if debug else 'INFO',
        'console_enabled': True,
        'file_enabled': True,
        'colorize': True,
    }
    init_logger(log_config)


def demo_backtest():
    """演示回测功能"""
    logger = get_logger(__name__)
    logger.info("开始演示回测功能")

    try:
        # 创建配置
        config = GlobalConfig.create_default()
        logger.info(f"配置创建完成: {config.app_name} v{config.app_version}")

        # 创建数据源
        data_source = create_data_source("mock", market_state="normal")
        logger.info("Mock数据源创建完成")

        # 获取测试数据
        symbol = "000001"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")

        logger.info(f"获取数据: {symbol}, {start_date} 到 {end_date}")
        data = data_source.get_data(symbol, start_date, end_date)

        logger.info(f"数据获取完成，共 {len(data)} 条记录")
        logger.info(f"数据范围: {data.index[0]} 到 {data.index[-1]}")
        logger.info(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")

        # 显示数据统计
        print("\n=== 数据统计 ===")
        print(f"股票代码: {symbol}")
        print(f"数据条数: {len(data)}")
        print(f"日期范围: {data.index[0].strftime('%Y-%m-%d')} 到 {data.index[-1].strftime('%Y-%m-%d')}")
        print(f"开盘价: {data['open'].iloc[0]:.2f}")
        print(f"收盘价: {data['close'].iloc[-1]:.2f}")
        print(f"最高价: {data['high'].max():.2f}")
        print(f"最低价: {data['low'].min():.2f}")
        print(f"平均成交量: {data['volume'].mean():.0f}")

        # 计算简单收益率
        total_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
        print(f"总收益率: {total_return:.2f}%")

        logger.info("演示完成")

    except Exception as e:
        logger.error(f"演示失败: {e}")
        raise


def list_data_sources():
    """列出可用的数据源"""
    from dataseed import list_data_sources, check_dependencies

    print("\n=== 可用数据源 ===")
    sources = list_data_sources()
    dependencies = check_dependencies()

    for name, class_name in sources.items():
        status = "✓" if dependencies.get(name, False) else "✗"
        print(f"{status} {name}: {class_name}")

    print("\n说明:")
    print("✓ - 可用")
    print("✗ - 不可用（缺少依赖）")


def show_config():
    """显示配置信息"""
    config = GlobalConfig.create_default()

    print("\n=== 系统配置 ===")
    runtime_info = config.get_runtime_info()

    print(f"应用名称: {runtime_info['app_name']}")
    print(f"应用版本: {runtime_info['app_version']}")
    print(f"运行环境: {runtime_info['environment']}")
    print(f"调试模式: {runtime_info['debug']}")

    print("\n目录配置:")
    for name, path in runtime_info['directories'].items():
        print(f"  {name}: {path}")

    print("\n功能特性:")
    for name, enabled in runtime_info['features'].items():
        status = "启用" if enabled else "禁用"
        print(f"  {name}: {status}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Quant_02 - 下一代高性能量化回测引擎",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py demo              # 运行演示
  python main.py sources           # 列出数据源
  python main.py config            # 显示配置
  python main.py demo --debug      # 调试模式运行演示
        """
    )

    parser.add_argument(
        'command',
        choices=['demo', 'sources', 'config'],
        help='要执行的命令'
    )

    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='Quant_02 v2.0.0'
    )

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.debug)

    # 执行命令
    try:
        if args.command == 'demo':
            demo_backtest()
        elif args.command == 'sources':
            list_data_sources()
        elif args.command == 'config':
            show_config()

    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"执行失败: {e}")
        if args.debug:
            raise
        sys.exit(1)


if __name__ == "__main__":
    main()
