"""
数据源基类模块

定义数据源的统一接口，所有具体的数据源实现都应该继承此基类。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, date
import pandas as pd
import time
from concurrent.futures import ThreadPoolExecutor

from ..core.interfaces import IDataSource
from ..core.exceptions import DataSourceException, DataNotAvailableException


class DataSource(IDataSource, ABC):
    """数据源基类
    
    提供数据获取的统一接口和基础功能：
    - 数据缓存
    - 错误重试
    - 并行获取
    - 数据验证
    - 性能监控
    """
    
    def __init__(
        self,
        name: str,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        retry_times: int = 3,
        retry_delay: float = 1.0,
        max_workers: int = 4,
        **kwargs
    ):
        """
        初始化数据源
        
        Args:
            name: 数据源名称
            cache_enabled: 是否启用缓存
            cache_ttl: 缓存生存时间（秒）
            retry_times: 重试次数
            retry_delay: 重试延迟（秒）
            max_workers: 最大工作线程数
            **kwargs: 其他参数
        """
        self.name = name
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.retry_times = retry_times
        self.retry_delay = retry_delay
        self.max_workers = max_workers
        
        # 内部状态
        self._cache: Dict[str, Tuple[pd.DataFrame, float]] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._stats = {
            'requests': 0,
            'cache_hits': 0,
            'errors': 0,
            'total_time': 0.0
        }
        
        # 初始化日志
        self._init_logger()
        self.logger.info(f"数据源初始化完成: {self.name}")
    
    def _init_logger(self):
        """初始化日志"""
        from ..utils.logger import get_logger
        self.logger = get_logger(f"{__name__}.{self.name}")
    
    @abstractmethod
    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """获取数据的具体实现
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数
            
        Returns:
            原始数据DataFrame
        """
        pass
    
    def get_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """获取数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            标准化的数据DataFrame
        """
        start_time = time.time()
        self._stats['requests'] += 1
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(symbol, start_date, end_date, frequency, **kwargs)
            
            # 检查缓存
            if use_cache and self.cache_enabled and self._is_cache_valid(cache_key):
                self._stats['cache_hits'] += 1
                self.logger.debug(f"缓存命中: {cache_key}")
                return self._cache[cache_key][0].copy()
            
            # 获取数据
            data = self._fetch_data_with_retry(symbol, start_date, end_date, frequency, **kwargs)
            
            # 数据验证
            self._validate_data(data)
            
            # 更新缓存
            if self.cache_enabled:
                self._cache[cache_key] = (data.copy(), time.time())
            
            execution_time = time.time() - start_time
            self._stats['total_time'] += execution_time
            
            self.logger.debug(f"数据获取完成: {symbol}, 耗时: {execution_time:.2f}秒")
            return data
            
        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"数据获取失败: {symbol}, 错误: {e}")
            raise DataSourceException(f"数据获取失败: {e}")
    
    def _fetch_data_with_retry(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """带重试的数据获取"""
        last_error = None
        
        for attempt in range(self.retry_times + 1):
            try:
                data = self._fetch_data(symbol, start_date, end_date, frequency, **kwargs)
                if not data.empty:
                    return data
                else:
                    raise DataNotAvailableException(f"数据为空: {symbol}")
                    
            except Exception as e:
                last_error = e
                if attempt < self.retry_times:
                    self.logger.warning(f"数据获取失败，第{attempt + 1}次重试: {symbol}, 错误: {e}")
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    break
        
        raise DataSourceException(f"数据获取失败，已重试{self.retry_times}次: {last_error}")
    
    def _generate_cache_key(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str,
        **kwargs
    ) -> str:
        """生成缓存键"""
        # 标准化日期格式
        start_str = self._format_date(start_date)
        end_str = self._format_date(end_date)
        
        # 生成键
        key_parts = [symbol, start_str, end_str, frequency]
        
        # 添加其他参数
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        
        return "|".join(key_parts)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False
        
        _, timestamp = self._cache[cache_key]
        return time.time() - timestamp < self.cache_ttl
    
    def _format_date(self, date_input: Union[str, date, datetime]) -> str:
        """格式化日期"""
        if isinstance(date_input, str):
            return date_input
        elif isinstance(date_input, (date, datetime)):
            return date_input.strftime("%Y-%m-%d")
        else:
            raise ValueError(f"不支持的日期格式: {type(date_input)}")
    
    def _validate_data(self, data: pd.DataFrame):
        """验证数据"""
        if data.empty:
            raise DataNotAvailableException("数据为空")
        
        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise DataSourceException(f"数据缺少必要列: {missing_columns}")
        
        # 检查数据类型
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                raise DataSourceException(f"列 {col} 不是数值类型")
        
        # 检查价格逻辑
        invalid_rows = (
            (data['high'] < data['low']) |
            (data['high'] < data['open']) |
            (data['high'] < data['close']) |
            (data['low'] > data['open']) |
            (data['low'] > data['close']) |
            (data['volume'] < 0)
        )
        
        if invalid_rows.any():
            self.logger.warning(f"发现 {invalid_rows.sum()} 行无效数据，将被过滤")
            data = data[~invalid_rows]
    
    def get_batch_data(
        self,
        symbols: List[str],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        parallel: bool = True,
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """批量获取数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            use_cache: 是否使用缓存
            parallel: 是否并行获取
            **kwargs: 其他参数
            
        Returns:
            股票代码到DataFrame的映射
        """
        if not parallel or len(symbols) <= 1:
            # 串行获取
            result = {}
            for symbol in symbols:
                try:
                    result[symbol] = self.get_data(
                        symbol, start_date, end_date, frequency, use_cache, **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"获取数据失败: {symbol}, 错误: {e}")
            return result
        
        # 并行获取
        result = {}
        futures = {}
        
        for symbol in symbols:
            future = self._executor.submit(
                self.get_data, symbol, start_date, end_date, frequency, use_cache, **kwargs
            )
            futures[future] = symbol
        
        for future in futures:
            symbol = futures[future]
            try:
                result[symbol] = future.result()
            except Exception as e:
                self.logger.error(f"并行获取数据失败: {symbol}, 错误: {e}")
        
        return result
    
    def get_available_symbols(self) -> List[str]:
        """获取可用标的列表"""
        # 默认实现，子类应该重写
        return []
    
    def is_available(self, symbol: str) -> bool:
        """检查标的是否可用"""
        return symbol in self.get_available_symbols()
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self.logger.info("缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_enabled': self.cache_enabled,
            'cache_size': len(self._cache),
            'cache_ttl': self.cache_ttl,
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        
        if stats['requests'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['requests']
            stats['error_rate'] = stats['errors'] / stats['requests']
            stats['avg_time'] = stats['total_time'] / stats['requests']
        else:
            stats['cache_hit_rate'] = 0.0
            stats['error_rate'] = 0.0
            stats['avg_time'] = 0.0
        
        return stats
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 尝试获取一个简单的数据
            symbols = self.get_available_symbols()
            if symbols:
                test_symbol = symbols[0]
                data = self.get_data(test_symbol, "2023-01-01", "2023-01-02")
                return not data.empty
            return True
        except Exception:
            return False
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=False)
